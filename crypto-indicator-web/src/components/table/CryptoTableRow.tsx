import React from "react";
import type { CryptoCurrencyStatisticsDto } from "../../generated";
import { SignalBadge } from "../signals/SignalBadge";
import { formatters, navigation } from "../../utils/formatters";
import { CURRENCIES, CSS_CLASSES } from "../../constants/app";

interface CryptoTableRowProps {
  crypto: CryptoCurrencyStatisticsDto;
  btcData?: any;
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
}

export const CryptoTableRow: React.FC<CryptoTableRowProps> = ({
  crypto,
  btcData,
  onSignalClick,
  formatDate,
}) => {
  const usdData = crypto.indicatorValues.find((el) => el);

  // Create tooltip text with last update information
  const usdTooltip = `Click to view chart${
    usdData?.timestamp ? ` • Last update: ${formatDate(usdData.timestamp)}` : ""
  }`;
  const btcTooltip = `Click to view chart${
    btcData?.timestamp ? ` • Last update: ${formatDate(btcData.timestamp)}` : ""
  }`;

  return (
    <tr key={crypto.symbol}>
      <td data-label="Cryptocurrency">
        <div className={CSS_CLASSES.SYMBOL_CELL}>
          <strong>{crypto.symbol}</strong>
        </div>
      </td>
      <td data-label="USD Price">
        <strong>{formatters.price(usdData?.close, CURRENCIES.USD)}</strong>
      </td>
      <td data-label="Market Cap">
        <strong>{formatters.marketCap(usdData?.marketCap)}</strong>
      </td>
      <td data-label="USD Signal">
        <SignalBadge
          color={usdData?.color}
          onClick={() => onSignalClick(crypto.symbol, CURRENCIES.USD)}
          clickable={true}
          title={usdTooltip}
        />
      </td>
      <td data-label="BTC Price">
        <strong>{formatters.price(btcData?.close, CURRENCIES.BTC)}</strong>
      </td>
      <td data-label="BTC Signal">
        <SignalBadge
          color={btcData?.color}
          onClick={() => onSignalClick(crypto.symbol, CURRENCIES.BTC)}
          clickable={true}
          title={btcTooltip}
        />
      </td>
    </tr>
  );
};
