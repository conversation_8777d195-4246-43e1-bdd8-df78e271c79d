import React, { useState } from 'react';
import type { CryptoCurrencyStatisticsDto } from '../../generated';
import { SignalBadge } from '../signals/SignalBadge';
import { formatters, navigation } from '../../utils/formatters';
import { CURRENCIES, CSS_CLASSES } from '../../constants/app';

interface CryptoCardProps {
  crypto: CryptoCurrencyStatisticsDto;
  btcData?: any;
  onSignalClick: (symbol: string, currency: string) => void;
  formatDate: (date?: string) => string;
}

export const CryptoCard: React.FC<CryptoCardProps> = ({
  crypto,
  btcData,
  onSignalClick,
  formatDate,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const usdData = crypto.indicatorValues.find((el) => el);

  const usdTooltip = `Click to view chart${
    usdData?.timestamp ? ` • Last update: ${formatDate(usdData.timestamp)}` : ""
  }`;
  const btcTooltip = `Click to view chart${
    btcData?.timestamp ? ` • Last update: ${formatDate(btcData.timestamp)}` : ""
  }`;

  return (
    <div className="crypto-card" onClick={() => setIsExpanded(!isExpanded)}>
      <div className="crypto-card-header">
        <div className="crypto-card-symbol">
          <div className={CSS_CLASSES.SYMBOL_CELL}>
            <strong>{crypto.symbol}</strong>
          </div>
        </div>
        <div className="crypto-card-price">
          <span className="price-label">USD</span>
          <span className="price-value">
            {formatters.price(usdData?.close, CURRENCIES.USD)}
          </span>
        </div>
      </div>

      <div className="crypto-card-signals">
        <div className="signal-group">
          <span className="signal-label">USD Signal</span>
          <div onClick={(e) => e.stopPropagation()}>
            <SignalBadge
              color={usdData?.color}
              onClick={() => onSignalClick(crypto.symbol, CURRENCIES.USD)}
              clickable={true}
              title={usdTooltip}
            />
          </div>
        </div>
        <div className="signal-group">
          <span className="signal-label">BTC Signal</span>
          <div onClick={(e) => e.stopPropagation()}>
            <SignalBadge
              color={btcData?.color}
              onClick={() => onSignalClick(crypto.symbol, CURRENCIES.BTC)}
              clickable={true}
              title={btcTooltip}
            />
          </div>
        </div>
      </div>

      {isExpanded && (
        <div className="crypto-card-details">
          <div className="detail-row">
            <span className="detail-label">Market Cap</span>
            <span className="detail-value">
              {formatters.marketCap(usdData?.marketCap)}
            </span>
          </div>
          <div className="detail-row">
            <span className="detail-label">BTC Price</span>
            <span className="detail-value">
              {formatters.price(btcData?.close, CURRENCIES.BTC)}
            </span>
          </div>
          {usdData?.timestamp && (
            <div className="detail-row">
              <span className="detail-label">Last Update</span>
              <span className="detail-value">
                {formatDate(usdData.timestamp)}
              </span>
            </div>
          )}
        </div>
      )}

      <div className="crypto-card-expand-indicator">
        <span className={`expand-icon ${isExpanded ? 'expanded' : ''}`}>
          ▼
        </span>
      </div>
    </div>
  );
};
